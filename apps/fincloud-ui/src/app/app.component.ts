import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { FinDocumentClassificationModule } from '@fincloud/ui/document-classification';
import { FinDropdownModule } from '@fincloud/ui/dropdown';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinInputModule } from '@fincloud/ui/input';
import { FinTextAreaModule } from '@fincloud/ui/text-area';
import { FinTreeMenuModule } from '@fincloud/ui/tree-menu';
import { FinSize } from '@fincloud/ui/types';
import {
  Observable,
  debounceTime,
  filter,
  fromEvent,
  interval,
  map,
  take,
} from 'rxjs';

@Component({
  standalone: true,
  imports: [
    CommonModule,
    FinIconModule,
    FinTreeMenuModule,
    FinInputModule,
    FinTextAreaModule,
    FinDocumentClassificationModule,
    FinDropdownModule,
  ],
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class FinAppComponent implements OnInit {
  expanded: any = false;
  readonly: any = true;
  disabled: any = false;
  loading: any = true;
  rootFolderId: any = undefined;
  aiPrediction: any = undefined;
  placeholder: any = '';
  aiEnabled = false;

  finSize = FinSize;

  formControl = new FormControl({
    value:
      'gengar-terrorgengargengar-terrorgengargengar-terrorgengargengar-terrorgengargengar-terrorgengargengar-terrorgengar.jpg',
    disabled: false,
  });
  formControl2 = new FormControl({
    value: 'apple',
    disabled: false,
  });

  private type({ word, speed }: { word: string; speed: number }) {
    return interval(speed).pipe(
      map((x) => word.substring(0, x + 1)),
      take(word.length),
    );
  }

  word$ = new Observable<string>();

  ngOnInit(): void {
    this.formControl.setErrors({
      aaaa: true,
    });

    fromEvent(window, 'wheel')
      .pipe(
        map(() => {
          const overlay = document.getElementsByClassName(
            'fin-autocomplete-block-scroll',
          )[0];
          overlay?.classList.remove('fin-autocomplete-block-scroll');

          return overlay;
        }),
        filter((overlay) => !!overlay),
        debounceTime(300),
        // takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((overlay) => {
        overlay.classList.add('fin-autocomplete-block-scroll');
      });

    this.word$ = this.type({
      word: 'gengar-terrorgengar.jpg',
      speed: 100,
    });
  }
  stop() {
    setTimeout(() => {
      this.loading = false;
    }, 1000);
    setTimeout(() => {
      this.aiPrediction = true;
    }, 2000);
    setTimeout(() => {
      this.rootFolderId = 'asdasd43534';
    }, 3000);
  }
}
